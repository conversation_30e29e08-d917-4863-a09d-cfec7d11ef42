# DBF风险排查功能 - 完整规则说明文档

## 📋 概述

DBF风险排查功能是同源数据智能识别工具的核心组件，用于识别金融交易数据中的潜在风险模式。本文档详细说明了所有风险检测规则、过滤逻辑和实现细节。

## 🔍 风险检测规则

### 1. 整数或近似整数转账风险
**风险标识**: `存在风险-整数或近似整数转账`

**检测条件**:
- 交易类型不是"查银行余额"或"查证券余额"
- 发生金额为10000的整数倍，或
- 发生金额大于10000且包含特定数字模式：999、998、001、111、222、333、444、555、666、777、888

**风险说明**: 整数转账或包含特定数字模式的转账可能表明人为操作或异常交易行为。

### 2. 临近发薪日交易风险
**风险标识**: `存在风险-临近发薪日交易`

**检测条件**:
- 交易类型不是"查银行余额"或"查证券余额"
- 发生金额 ≥ 10000元
- 交易日期为每月的10、11、15、16日

**风险说明**: 在发薪日附近进行大额交易可能存在资金转移风险。

### 3. 月初月末交易风险
**风险标识**: `存在风险-月初月末交易`

**检测条件**:
- 交易类型不是"查银行余额"或"查证券余额"
- 发生金额 ≥ 10000元
- 交易日期为每月的1、2、3、29、30、31日

**风险说明**: 月初月末的大额交易可能与财务结算或资金转移相关。

### 4. 试密过程异常风险
**风险标识**: `疑似他人操作-试密过程异常`

**检测条件**:
- 同一客户同一日期的银行错误信息中同时包含：
  - 成功关键词：交易成功、转账成功
  - 密码关键词：密码

**风险说明**: 在密码相关错误后出现成功交易，可能表明他人操作或密码被破解。

### 5. 单日内频繁操作风险
**风险标识**: `存在风险-单日内频繁操作`

**检测条件**:
- 同一客户同一日期的交易记录数 > 15条

**风险说明**: 单日内过于频繁的操作可能表明异常交易行为。

### 6. 资金定期定额转出风险
**风险标识**: `存在风险-资金定期定额转出`

**检测条件**:
- 交易类型不是"查银行余额"或"查证券余额"
- 同一客户连续三个月在相同日期（±1天容差）进行相似金额（±10000元容差）的转账
- 所有三笔交易金额都 ≥ 10000元

**风险说明**: 定期定额的资金转出可能表明有组织的资金转移行为。

### 7. 非正常有效户撤资风险（新增）
**风险标识**: `存在风险-达成有效户后资金清零`

**检测条件**:
- 交易类型不是"查银行余额"或"查证券余额"
- 发生金额在10000-10002元之间（包含）
- 后资金额 < 0.1元

**风险说明**: 在达成有效户条件后立即清空资金，可能表明虚假开户或套利行为。

## 🚫 通用过滤规则

### 交易类型过滤
所有风险检测规则（除试密异常和频繁操作外）都会自动排除以下交易类型：
- `查银行余额`
- `查证券余额`

**过滤原因**: 余额查询操作不涉及资金转移，不应被标记为风险交易。

## 📊 风险检测流程

### 阶段1: 数据加载
- 支持CSV和Parquet格式
- 自动转换数据类型为字符串，防止科学计数法问题
- 验证必要列的存在性

### 阶段2: 单笔交易风险检测
按行检测以下风险：
1. 整数或近似整数转账风险
2. 临近发薪日交易风险
3. 月初月末交易风险
4. 非正常有效户撤资风险

### 阶段3: 客户级别风险检测
按客户和日期分组检测：
1. 试密过程异常风险
2. 单日内频繁操作风险

### 阶段4: 复杂模式风险检测
1. 资金定期定额转出风险（跨月分析）

### 阶段5: 结果保存
- 只保留有风险信息的记录
- 风险信息列置于第一列
- 防止长数字字段科学计数法损坏

## 🔧 技术实现细节

### 数据保护机制
- 长数字字段（银行账号、身份证号等）强制转换为字符串
- CSV保存时使用特殊格式选项防止科学计数法
- 支持Parquet格式的高效存储

### 性能优化
- 分块处理大数据集
- 内存监控和动态调整
- 智能缓存机制

### 错误处理
- 完善的异常捕获机制
- 详细的日志记录
- 数据验证和容错处理

## 📈 使用统计

风险检测完成后，系统会提供详细的统计信息：
- 总记录数和风险记录数
- 各类风险的数量和比例
- 处理速度和耗时统计

## 🎯 最佳实践

### 数据准备
1. 确保数据包含必要列：发生金额、交易日期、客户编号、银行错误信、交易类型、后资金额
2. 数据格式应为CSV或Parquet
3. 日期格式应为YYYYMMDD

### 结果分析
1. 优先关注风险信息列（已置于第一列）
2. 结合业务场景分析风险的合理性
3. 注意多种风险类型的组合出现

### 性能考虑
1. 大数据集建议使用Parquet格式
2. 确保足够的内存空间
3. 在网络稳定环境下进行IP归属地查询

## 📝 版本信息

- **当前版本**: 风险排查功能增强版
- **更新日期**: 2025-07-10
- **新增功能**: 非正常有效户撤资风险检测、增强过滤逻辑、科学计数法保护
- **兼容性**: 完全向后兼容

---

📞 **技术支持**: 如有问题请查看处理日志或联系技术支持团队。
