#!/usr/bin/env python3
"""
测试IP地址缓存清理功能
"""

import json
import os
import sys

# 创建一个包含错误信息的测试缓存文件
def create_test_cache():
    """创建包含错误信息的测试缓存文件"""
    test_cache = {
        "***********": {
            "location": "中国北京市",
            "coordinates": "116.4074,39.9042"
        },
        "********": {
            "location": "未知",
            "coordinates": ""
        },
        "***************": {
            "location": "查询失败: HTTPSConnectionPool(host='apimobile.meituan.com', port=443): Max retries exceeded with url: /locate/v2/ip/loc?rgeo=true&ip=*************** (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001AFB02B8D40>: Failed to resolve 'apimobile.meituan.com' ([<PERSON>rrno 11001] getaddrinfo failed)\"))",
            "coordinates": ""
        },
        "*******": {
            "location": "美国加利福尼亚州",
            "coordinates": "37.4419,-122.1419"
        },
        "*******": "查询出错: Connection timeout",
        "*******": "查询失败",
        "*******": {
            "location": "查询失败: requests.exceptions.ConnectionError",
            "coordinates": ""
        }
    }
    
    cache_file = "test_ip_location_cache.json"
    with open(cache_file, 'w', encoding='utf-8') as f:
        json.dump(test_cache, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 创建测试缓存文件: {cache_file}")
    print(f"📊 测试数据: {len(test_cache)} 条记录")
    return cache_file

def test_cache_cleanup():
    """测试缓存清理功能"""
    # 导入必要的类
    sys.path.append('.')
    from merge_dbf import IPLocationQuerier
    
    # 创建测试缓存文件
    test_cache_file = create_test_cache()
    
    try:
        # 创建一个临时的IPLocationQuerier实例来测试缓存清理
        class TestIPQuerier(IPLocationQuerier):
            def __init__(self):
                # 最小化初始化，只设置必要的属性
                self.cache_file_path = test_cache_file
                self.persistent_cache = {}
                self.cache_write_lock = __import__('threading').Lock()
                
            def _log(self, message):
                print(f"[LOG] {message}")
                
            def _normalize_ip_for_cache(self, ip_str: str) -> str:
                """简化的IP标准化方法"""
                return ip_str.strip() if ip_str else ""
                
            def _write_cache_file_atomic(self):
                """简化的缓存写入方法"""
                with open(self.cache_file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.persistent_cache, f, ensure_ascii=False, indent=2)
        
        # 创建测试实例
        querier = TestIPQuerier()
        
        print("\n🧪 开始测试缓存清理功能...")
        
        # 加载并清理缓存
        querier._load_cache_from_file()
        
        print(f"\n📊 清理结果:")
        print(f"   有效记录数: {len(querier.persistent_cache)}")
        
        # 显示保留的记录
        print(f"\n✅ 保留的有效记录:")
        for ip, data in querier.persistent_cache.items():
            if isinstance(data, dict):
                location = data.get('location', '')
            else:
                location = str(data)
            print(f"   {ip}: {location}")
        
        # 验证清理结果
        expected_valid_ips = ["***********", "********", "*******"]
        actual_ips = list(querier.persistent_cache.keys())
        
        print(f"\n🔍 验证结果:")
        success = True
        for expected_ip in expected_valid_ips:
            if expected_ip in actual_ips:
                print(f"   ✅ {expected_ip}: 正确保留")
            else:
                print(f"   ❌ {expected_ip}: 意外移除")
                success = False
        
        # 检查错误记录是否被正确移除
        error_ips = ["***************", "*******", "*******", "*******"]
        for error_ip in error_ips:
            if error_ip not in actual_ips:
                print(f"   ✅ {error_ip}: 正确移除错误记录")
            else:
                print(f"   ❌ {error_ip}: 错误记录未被移除")
                success = False
        
        if success:
            print(f"\n🎉 缓存清理功能测试通过！")
        else:
            print(f"\n❌ 缓存清理功能测试失败！")
            
        return success
        
    finally:
        # 清理测试文件
        if os.path.exists(test_cache_file):
            os.remove(test_cache_file)
            print(f"\n🗑️ 清理测试文件: {test_cache_file}")

if __name__ == "__main__":
    test_cache_cleanup()
