#!/usr/bin/env python3
"""
简单的缓存验证测试
"""

import json
import os

def test_validation_logic():
    """测试缓存验证逻辑"""
    
    # 测试数据
    test_cases = [
        # (location_data, expected_valid, description)
        ({"location": "中国北京市", "coordinates": "116.4074,39.9042"}, True, "正常位置信息"),
        ({"location": "未知", "coordinates": ""}, True, "未知位置（应保留）"),
        ({"location": "查询失败", "coordinates": ""}, False, "简单错误标识"),
        ({"location": "查询失败: HTTPSConnectionPool error", "coordinates": ""}, False, "网络错误"),
        ({"location": "查询出错: Connection timeout", "coordinates": ""}, False, "连接超时"),
        ("查询失败", False, "旧格式简单错误"),
        ("中国上海市", True, "旧格式正常位置"),
        ({"location": "查询失败: Max retries exceeded", "coordinates": ""}, False, "重试超限错误"),
    ]
    
    # 模拟验证函数
    def is_valid_cache_entry(location_data):
        """简化的验证逻辑"""
        # 处理字典格式数据
        if isinstance(location_data, dict):
            location_value = location_data.get('location', '')
        # 处理字符串格式数据
        elif isinstance(location_data, str):
            location_value = location_data
        else:
            return False, 'other'
        
        if not location_value:
            return False, 'other'
        
        location_str = str(location_value).strip()
        
        # 检查网络错误模式
        network_error_patterns = [
            '查询失败:',
            'HTTPSConnectionPool',
            'Max retries exceeded',
            'NameResolutionError',
            'getaddrinfo failed',
            'Connection timeout',
            'SSL error',
            'ConnectionError',
            'Timeout',
            'requests.exceptions',
            'urllib3.exceptions'
        ]
        
        for pattern in network_error_patterns:
            if pattern in location_str:
                return False, 'network'
        
        # 检查解析错误模式
        parse_error_patterns = [
            '查询出错:',
            '解析失败',
            'JSON decode error',
            'KeyError',
            'ValueError',
            'AttributeError'
        ]
        
        for pattern in parse_error_patterns:
            if pattern in location_str:
                return False, 'parse'
        
        # 检查简单错误标识（这些不应该被缓存）
        simple_error_values = ['查询失败', '查询出错', '解析失败']
        if location_str in simple_error_values:
            return False, 'other'
        
        # 检查是否以错误前缀开头
        error_prefixes = ['查询失败:', '查询出错:', '解析失败:', 'Error:', 'Exception:']
        for prefix in error_prefixes:
            if location_str.startswith(prefix):
                return False, 'other'
        
        # 保留"未知"作为有效值（这是对私有/保留IP的正常响应）
        if location_str == '未知':
            return True, ''
        
        # 如果包含明显的异常堆栈信息，则认为是错误
        if any(keyword in location_str for keyword in ['Traceback', 'File "', 'line ', 'in ', 'raise ']):
            return False, 'other'
        
        # 其他情况认为是有效的位置信息
        return True, ''
    
    print("🧪 开始测试缓存验证逻辑...")
    print("=" * 60)
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, (location_data, expected_valid, description) in enumerate(test_cases, 1):
        is_valid, error_type = is_valid_cache_entry(location_data)
        
        if is_valid == expected_valid:
            status = "✅ 通过"
            success_count += 1
        else:
            status = "❌ 失败"
        
        print(f"测试 {i:2d}: {status} - {description}")
        print(f"        数据: {location_data}")
        print(f"        预期: {'有效' if expected_valid else '无效'}, 实际: {'有效' if is_valid else '无效'}")
        if not is_valid and error_type:
            print(f"        错误类型: {error_type}")
        print()
    
    print("=" * 60)
    print(f"测试结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！缓存验证逻辑工作正常。")
        return True
    else:
        print("❌ 部分测试失败，需要检查验证逻辑。")
        return False

if __name__ == "__main__":
    test_validation_logic()
